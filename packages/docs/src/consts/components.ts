import components from './components.json'

const componentsMenus = components.map(({ name, camelized }) => {
  return {
    label: camelized,
    path: `/components/${name}`,
  }
})

export const asideMenus = [
  {
    label: 'Guide',
    children: [
      {
        label: 'Introduction',
        path: '/guide/introduction',
      },
      {
        label: 'Installation',
        path: '/guide/installation',
      },
      {
        label: 'FAQ',
        path: '/guide/faq',
      },
    ],
  },
  {
    label: 'Components',
    children: [
      {
        label: 'Overview',
        path: '/components',
      },
      ...componentsMenus,
    ],
  },
]
