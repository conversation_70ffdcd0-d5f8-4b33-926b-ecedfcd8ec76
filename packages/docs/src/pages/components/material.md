# Material
Various surfaces with shadows, built on top of `<Stack>`.

## Variants

```vue demo
<template>
  <PStack direction="vertical">
    <PMaterial variant="default">
      <div class="w-full h-30"></div>
    </PMaterial>
    <PMaterial variant="small">
      <div class="w-full h-30"></div>
    </PMaterial>
    <PMaterial variant="medium">
      <div class="w-full h-30"></div>
    </PMaterial>
    <PMaterial variant="large">
      <div class="w-full h-30"></div>
    </PMaterial>
    <PMaterial variant="tooltip">
      <div class="w-full h-30"></div>
    </PMaterial>
    <PMaterial variant="menu">
      <div class="w-full h-30"></div>
    </PMaterial>
    <PMaterial variant="modal">
      <div class="w-full h-30"></div>
    </PMaterial>
    <PMaterial variant="fullscreen">
      <div class="w-full h-30"></div>
    </PMaterial>
  </PStack>
</template>
```
