{"name": "@pxd-ui/cli", "version": "0.0.1", "description": "pxd cli", "author": "lib<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/libondev/pxd#readme", "repository": {"type": "git", "url": "git+https://github.com/libondev/pxd.git", "directory": "packages/cli"}, "bugs": {"url": "https://github.com/libondev/pxd/issues"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "bin": {"pxd": "dist/index.js"}, "files": ["dist"], "scripts": {"dev": "unbuild --stub", "build": "unbuild", "prepublishOnly": "unbuild"}, "dependencies": {"@clack/prompts": "catalog:", "cross-spawn": "catalog:", "mri": "catalog:"}, "devDependencies": {"mkdist": "catalog:", "typescript": "catalog:", "unbuild": "catalog:"}}