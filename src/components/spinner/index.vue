<script setup lang="ts">
defineOptions({
  name: 'PSpin<PERSON>',
})

const ITEMS_COUNT = 12
const ROTATE_STEP = 360 / ITEMS_COUNT
const OPACITY_STEP = 1 / ITEMS_COUNT
</script>

<template>
  <div class="pxd-spinner transform-origin-center pointer-events-none relative size-em overflow-hidden text-gray-700 motion-safe:animate-spin">
    <div class="pxd-spinner-container top-0 left-0 absolute size-full">
      <div
        v-for="i of ITEMS_COUNT"
        :key="i"
        class="pxd-spinner-item absolute rounded-sm bg-current"
        :style="{
          left: '38%',
          top: '46%',
          width: '24%',
          height: '8%',
          opacity: i * OPACITY_STEP,
          transform: `rotate(${i * ROTATE_STEP}deg) translate(146%)`,
        }"
      />
    </div>
  </div>
</template>
