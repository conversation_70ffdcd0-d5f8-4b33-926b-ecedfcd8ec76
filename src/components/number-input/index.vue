<script lang="ts" setup>
import type { InputProps } from '../../types/components/input'
import { useModelValue } from '../../composables/useModelValue'
import PInput from '../input/index.vue'

interface Props extends InputProps {
  modelValue?: string | number | null
  scientific?: boolean
}

defineOptions({
  name: 'PNumberInput',
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
})

const props = withDefaults(
  defineProps<Props>(),
  {
    scientific: true,
  },
)

const emits = defineEmits<{
  'update:modelValue': [number]
}>()

const computedModelValue = useModelValue(props, emits, {
  get: () => {
    return formatModelValue(props.modelValue)
  },
})

function formatModelValue<T>(value: T): number {
  const digitizeValue = Number(value)

  if (Number.isNaN(digitizeValue)) {
    return 0
  }

  return digitizeValue
}

function onInputChange(ev: InputProps['modelValue']) {
  const digitizeValue = formatModelValue(ev)
  console.info('🔥index.vue:40/(digitizeValue):\n', digitizeValue)
}

function onInputKeydown(ev: KeyboardEvent) {
  console.info('🍀index.vue:44/(ev):\n', ev)

  if (!props.scientific && ['e', 'E'].includes(ev.key)) {
    ev.preventDefault()
  }

  if ([].includes()) { }
}
</script>

<template>
  {{ computedModelValue }} - {{ typeof computedModelValue }}
  <PInput
    inputmode="decimal"
    :model-value="computedModelValue"
    @keydown="onInputKeydown"
    @update:model-value="onInputChange"
  />
</template>
