{"version": 1.1, "atDirectives": [{"name": "@tailwind", "description": "Use the `@tailwind` directive to insert Tailwind's `base`, `components`, `utilities` and `screens` styles into your CSS.", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#tailwind"}]}, {"name": "@source", "description": "Use the @source directive to explicitly specify source files that aren't picked up by Tailwind's automatic content detection.", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/v4-beta#adding-content-sources"}]}, {"name": "@config", "description": "Use the @config directive to load a legacy JavaScript-based configuration file", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#config"}]}, {"name": "@apply", "description": "Use the @apply directive to inline any existing utility classes into your own custom CSS", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#apply"}]}, {"name": "@responsive", "description": "You can generate responsive variants of your own classes by wrapping their definitions in the `@responsive` directive:\n```css\n@responsive {\n  .alert {\n    background-color: #E53E3E;\n  }\n}\n```\n", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#responsive"}]}, {"name": "@screen", "description": "The `@screen` directive allows you to create media queries that reference your breakpoints by **name** instead of duplicating their values in your own CSS:\n```css\n@screen sm {\n  /* ... */\n}\n```\n…gets transformed into this:\n```css\n@media (min-width: 640px) {\n  /* ... */\n}\n```\n", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#screen"}]}, {"name": "@variants", "description": "Generate `hover`, `focus`, `active` and other **variants** of your own utilities by wrapping their definitions in the `@variants` directive:\n```css\n@variants hover, focus {\n   .btn-brand {\n    background-color: #3182CE;\n  }\n}\n```\n", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#variants"}]}, {"name": "@theme", "description": "Use the @theme directive to define your project's custom design tokens, like fonts, colors, and breakpoints", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#theme-directive"}]}, {"name": "@utility", "description": "Use the @utility directive to add a custom utility to your project", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#theme-directive"}]}, {"name": "@custom-variant", "description": "add your own custom variants using the @custom-variant directive", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#theme-directive"}]}]}